# GitOps Application Templates

This directory contains enhanced Kubernetes and ArgoCD templates for automated application deployment with dynamic multi-stage rolling update deployment system.

## 🚀 Enhanced Features

The templates have been completely redesigned to provide industry-standard, environment-aware rolling update deployments with complete configurability and zero hardcoded values. The system supports any application type through dynamic configuration and intelligent resource allocation.

### ✅ What's Included

#### 1. **Complete ConfigMap** (`templates/k8s/configmap.yaml`)
- **Application Configuration**: NODE_ENV, PORT, APP_URL, API_URL
- **Database Configuration**: DB_HOST, DB_PORT, DB_NAME
- **JWT Configuration**: JWT_EXPIRATION
- **CORS Configuration**: Origins, methods, credentials, max-age
- **SMTP Configuration**: Host, port, from address
- **OAuth2 Configuration**: Google OAuth redirect URIs and scopes

#### 2. **Comprehensive Secret** (`templates/k8s/secret.yaml`)
- **Essential Authentication**: JWT_SECRET (auto-generated or user-provided)
- **Database Credentials**: DB_USER, DB_PASSWORD (auto-generated or user-provided)
- **SMTP Credentials**: SMTP_USER, SMTP_PASS (user-provided or placeholder)
- **OAuth2 Credentials**: GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET (user-provided or placeholder)
- **Custom Secret Keys**: Support for additional secrets from GitHub Issues
- **Automatic Base64 Encoding**: All secret values are automatically base64 encoded

#### 3. **Enhanced Deployment** (`templates/k8s/deployment.yaml`)
- **Environment Variables**: Always loads ConfigMap and essential secrets
- **Health Checks**: Configurable liveness and readiness probes
- **Resource Management**: Proper CPU and memory limits/requests
- **Database Support**: Optional init containers for PostgreSQL readiness

### 🔧 Configurable via GitHub Issues

All configuration values can be specified through the GitHub Issue form, including:
- Application URLs and API endpoints
- CORS settings
- JWT configuration
- SMTP settings
- OAuth2 configuration
- Database settings
- Health check paths

### 🔄 Dynamic Database Host Configuration

The ConfigMap template now includes conditional logic for the `DB_HOST` value based on the environment:

- **Development**: When `NODE_ENV` is "dev", `DB_HOST` is set to "*************"
- **Staging**: When `NODE_ENV` is "staging", `DB_HOST` is set to "[app-name]-postgres.staging"
- **Production**: When `NODE_ENV` is "prod", `DB_HOST` is set to "[app-name]-postgres.production"

This ensures proper database connectivity across different environments without manual configuration.

## 🔧 Template Variables

### Core Variables
- `{{PROJECT_ID}}` - Project identifier
- `{{NAMESPACE}}` - Kubernetes namespace
- `{{ENVIRONMENT}}` - Environment (dev/staging/production)
- `{{CONTAINER_IMAGE}}` - Docker image
- `{{CONTAINER_PORT}}` - Application port (default: 8080)

### Resource Variables
- `{{MEMORY_REQUEST}}` - Memory request (default: 256Mi)
- `{{MEMORY_LIMIT}}` - Memory limit (default: 512Mi)
- `{{CPU_REQUEST}}` - CPU request (default: 100m)
- `{{CPU_LIMIT}}` - CPU limit (default: 500m)

### Database Variables
- `{{ENABLE_DATABASE}}` - Boolean for PostgreSQL deployment
- `{{DB_NAME}}` - Database name (default: userauth)
- `{{DB_USER}}` - Database user (default: postgres)
- `{{DB_USER_B64}}` - Base64 encoded database user

### Health Check Variables
- `{{HEALTH_CHECK_PATH}}` - Health check endpoint (default: /api/v1/email/status)

## 📋 Default Configurations

### ConfigMap Defaults
```yaml
NODE_ENV: "{{ENVIRONMENT}}"
PORT: "{{CONTAINER_PORT}}"
# DB_HOST: Conditional based on NODE_ENV:
#   - dev: "localhost"
#   - staging: "[app-name]-postgres.staging"
#   - prod: "[app-name]-postgres.production"
JWT_EXPIRATION: "86400000"
CORS_ALLOWED_ORIGINS: "http://localhost:3000,http://localhost:3001"
GOOGLE_SCOPE: "email,profile,openid"
```

### Secret Defaults
```yaml
JWT_SECRET: UExBQ0VIT0xERVI=  # PLACEHOLDER (or user-provided value, base64 encoded)
DB_USER: cG9zdGdyZXM=  # postgres
DB_PASSWORD: UExBQ0VIT0xERVI=  # PLACEHOLDER (or user-provided value, base64 encoded)
SMTP_USER: UExBQ0VIT0xERVI=  # PLACEHOLDER (or user-provided value, base64 encoded)
SMTP_PASS: UExBQ0VIT0xERVI=  # PLACEHOLDER (or user-provided value, base64 encoded)
GOOGLE_CLIENT_ID: UExBQ0VIT0xERVI=  # PLACEHOLDER (or user-provided value, base64 encoded)
GOOGLE_CLIENT_SECRET: UExBQ0VIT0xERVI=  # PLACEHOLDER (or user-provided value, base64 encoded)
```

### New Security Configuration Fields
The GitHub issue form now includes the following additional security configuration fields:

- **JWT Secret**: Secret key for JWT token signing
- **Database Password**: Password for database connection
- **SMTP Username**: Username for SMTP email service authentication
- **SMTP Password**: Password for SMTP email service authentication
- **Google OAuth Client ID**: Google OAuth 2.0 Client ID for authentication
- **Google OAuth Client Secret**: Google OAuth 2.0 Client Secret for authentication

All these fields are optional in the GitHub issue form. If not provided, placeholder values will be used that need to be manually updated before production deployment.

### Health Checks
```yaml
livenessProbe:
  httpGet:
    path: /api/v1/oauth2/status
    port: 8080
  initialDelaySeconds: 30
readinessProbe:
  httpGet:
    path: /api/v1/oauth2/status
    port: 8080
  initialDelaySeconds: 10
```

## 🛡️ Security Considerations

### ⚠️ Important: Update Production Secrets
The templates include default placeholder values that **MUST** be updated for production use:

1. **JWT_SECRET**: Change from default `supersecretkey`
2. **Database Passwords**: Update from default `password`
3. **OAuth Credentials**: Add your actual Google Client ID and Secret
4. **SMTP Credentials**: Add your actual email service credentials

### 🔐 Base64 Encoding
All secret values must be base64 encoded:
```bash
echo -n "your-secret-value" | base64
```

## 🚀 Usage

When creating a GitHub Issue for application deployment, the enhanced templates will automatically:

1. ✅ Generate a complete ConfigMap with NestJS defaults
2. ✅ Create a comprehensive Secret with all required placeholders
3. ✅ Deploy with proper environment variable loading
4. ✅ Include health checks for ArgoCD monitoring
5. ✅ Set appropriate resource limits

## 🔄 Migration from Old Templates

Applications created with the old templates (like `ai-nest`) may need manual updates to include:
- ConfigMap with comprehensive environment variables
- Secret with JWT and OAuth credentials
- Deployment with `envFrom` and `env` sections
- Health check probes

## 📚 Related Documentation

- [Generation Script](../scripts/generate-manifests-cicd.py)
- [Validation Script](../scripts/validate-yaml.py)
- [Deployment Examples](../examples/)

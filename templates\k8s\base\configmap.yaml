apiVersion: v1
kind: ConfigMap
metadata:
  name: RESOURCE_NAME-config
  namespace: NAMESPACE
  labels:
    app: RESOURCE_NAME
    app.kubernetes.io/name: RESOURCE_NAME
    app.kubernetes.io/component: APP_TYPE
    app.kubernetes.io/version: "APP_VERSION"
    app.kubernetes.io/managed-by: argocd
    app.kubernetes.io/part-of: RESOURCE_NAME
    environment: ENVIRONMENT
    # Display name for human-readable identification
    app.display-name: "APP_NAME"
  annotations:
    argocd.argoproj.io/sync-wave: "1"
data:
  # Application Configuration
  NODE_ENV: "ENVIRONMENT"
  PORT: "CONTAINER_PORT"
  APP_NAME: "APP_NAME"
  APP_TYPE: "APP_TYPE"

  # Database Configuration (if enabled)
  DB_HOST: "DB_HOST"
  DB_PORT: "DB_PORT"
  DB_NAME: "DB_NAME"
  DB_USER: "DB_USER"

  # SMTP Configuration
  SMTP_HOST: "SMTP_HOST"
  SMTP_PORT: "SMTP_PORT"
  SMTP_FROM: "SMTP_FROM"

  # Dynamic Backend Configuration (for frontend applications)
  {{#if BACKEND_TYPE}}API_URL: "API_URL"
  BACKEND_TYPE: "BACKEND_TYPE"
  BACKEND_PROJECT_ID: "BACKEND_PROJECT_ID"
  BACKEND_PORT: "BACKEND_PORT"
  BACKEND_NAMESPACE: "BACKEND_NAMESPACE"{{/if}}

  # Frontend-specific environment variables
  {{#if IS_FRONTEND}}REACT_APP_API_URL: "API_URL"
  REACT_APP_BACKEND_TYPE: "BACKEND_TYPE"
  VUE_APP_API_URL: "API_URL"
  VUE_APP_BACKEND_TYPE: "BACKEND_TYPE"
  ANGULAR_API_URL: "API_URL"
  ANGULAR_BACKEND_TYPE: "BACKEND_TYPE"{{/if}}

  # Service Discovery Configuration
  SERVICE_NAME: "RESOURCE_NAME-service"
  SERVICE_NAMESPACE: "NAMESPACE"
  SERVICE_PORT: "CONTAINER_PORT"

  # Application-specific configurations will be added by overlays

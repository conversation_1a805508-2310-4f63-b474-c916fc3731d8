apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

metadata:
  name: dev-overlay
  annotations:
    config.kubernetes.io/local-config: "true"

# Reference to base configuration
resources:
- ../../base

# Environment-specific namespace
namespace: APP_NAME-dev

# Environment-specific name prefix
namePrefix: ""

# Common labels for development environment
commonLabels:
  environment: dev
  deployment.tier: development

# Common annotations for development environment
commonAnnotations:
  argocd.argoproj.io/sync-options: "CreateNamespace=true"
  deployment.environment: "dev"

# Development-specific image configuration
images:
- name: CONTAINER_IMAGE
  newTag: DOCKER_TAG

# ConfigMap generator for environment-specific variables
configMapGenerator:
- name: kustomize-vars
  literals:
  - APP_NAME=APP_NAME
  - NAMESPACE=APP_NAME-dev
  - ENVIRONMENT=dev
  - APP_TYPE=APP_TYPE
  - APP_VERSION=APP_VERSION
  - CONTAINER_PORT=CONTAINER_PORT
  - HEALTH_CHECK_PATH=HEALTH_CHECK_PATH
  - HEALTH_CHECK_PORT=CONTAINER_PORT
  - HEALTH_CHECK_SCHEME=HTTP
  - SERVICE_TYPE=LoadBalancer
  - IMAGE_PULL_POLICY=Always
  - TERMINATION_GRACE_PERIOD=30
  - DB_HOST=*************
  - DB_PORT=5432
  - DB_NAME=APP_NAME
  - DB_USER=postgres
  - SMTP_HOST=smtp.gmail.com
  - SMTP_PORT=587
  - SMTP_FROM=<EMAIL>

# Environment-specific patches
patches:
- path: deployment-patch.yaml
  target:
    kind: Deployment
    name: APP_NAME
- path: resource-patch.yaml
  target:
    kind: ResourceQuota
    name: APP_NAME-resource-quota
- path: security-patch.yaml
  target:
    kind: Deployment
    name: APP_NAME

# Remove HPA in development (disabled)
patchesJson6902:
- target:
    version: v2
    kind: HorizontalPodAutoscaler
    name: APP_NAME-hpa
  path: disable-hpa-patch.yaml

# Remove Network Policy in development (disabled)
- target:
    version: v1
    kind: NetworkPolicy
    name: APP_NAME-network-policy
  path: disable-network-policy-patch.yaml
